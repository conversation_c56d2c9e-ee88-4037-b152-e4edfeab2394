# Virtual Environment
.venv/
venv/
ENV/

# Python
__pycache__/
*.py[cod]
*.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# ignore __pychache__ in src
src/__pycache__
src/**/__pycache__

# Data files
data/logs/
data/raw/*
data/processed/*
!data/raw/.gitkeep
!data/processed/.gitkeep

# Specific data file formats (except when in specific sample directories)
## Excel files
*.xlsx
*.xls
## CSV files
*.csv
## JSON files
*.json
## Parquet files
*.parquet
## Avro files
*.avro
## XML files
*.xml
## ORC files
*.orc

# Environment variables
.env

# IDE
.idea/
.vscode/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

transform.py
# ignore _ docker files
docker-compose.yml
docker-entrypoint.sh
Dockerfile


src_mode/
app_workflow_tree.md
src/frontend/pages/utils
src/backend/core/preprocessing/utils
src/frontend/pages/structural_issues
src/backend/core/preprocessing/column_mapper.py
data_engineering/
kubernetes/

#
technical_documentation.md

# ignore all the __pycache__ in the codebase
**/__pycache__
**/**/__pycache__ 
**/**/**/__pycache__
**/**/**/**/__pycache__
