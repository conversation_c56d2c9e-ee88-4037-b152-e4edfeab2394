[build-system]
requires = ["hatchling>=1.18.0"]
build-backend = "hatchling.build"

[project]
name = "dynamic-data-analysis-platform"
version = "0.1.0"
description = "A platform for automated data analysis with descriptive, diagnostic, predictive, and prescriptive capabilities"
readme = "README.md"
requires-python = ">=3.10"
license = {text = "MIT"}
authors = [
    {name = "<PERSON><PERSON><PERSON><PERSON> Tangnami", email = "<EMAIL>"},
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.13",
]
dependencies = [
     "fastapi[standard]>=0.105.0",
     "uvicorn>=0.24.0",
     "streamlit>=1.29.0",
     "typer>=0.9.0",
     "numpy>=1.26.2",
     "scikit-learn>=1.3.2",
     "matplotlib>=3.9.0",
     "seaborn>=0.13.0",
     "plotly>=5.18.0",
     "python-multipart>=0.0.6",
     "openpyxl>=3.1.2",
     "pydantic>=2.5.2",
     "pydantic-settings>=2.0.0",
     "httpx>=0.25.2",
     "sqlalchemy>=2.0.23",
     "python-dotenv>=1.0.0",
     "joblib>=1.3.2",
     "tqdm>=4.66.1",
     "pandas>=2.2.3",
     "polars>=1.27.1",
     "requests>=2.32.3",
     "aiofiles>=24.1.0",
     "pyarrow>=19.0.1",
     "colorama>=0.4.6",
     "pyspark>=3.5.5",
     "networkx>=3.4.2",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
]

[project.scripts]
ddap = "src.main:main"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.hatch.build.targets.sdist]
include = [
    "src",
    "tests",
    "README.md",
    "LICENSE",
]
exclude = [
    "*.pyc",
    "__pycache__",
    "*.so",
    ".git*",
    ".mypy_cache",
]

python = ">=3.9"
