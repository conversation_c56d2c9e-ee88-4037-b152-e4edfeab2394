from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, List
import tempfile
import os
from simple_engines import get_engine

app = FastAPI(
    title="Simple Data Analysis Comparison Tool",
    description="Compare pandas, polars, and pyspark for data analysis",
    version="2.0.0"
)

# Add CORS middleware for Streamlit frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:8501"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Simple in-memory storage for demo
data_storage = {}

# Request models
class AnalysisRequest(BaseModel):
    file_id: str
    engine_type: str  # pandas, polars, pyspark
    analysis_type: str  # basic_stats, missing_values, correlations, group_analysis

class PreprocessRequest(BaseModel):
    file_id: str
    engine_type: str
    operations: List[Dict[str, Any]]

class ComparisonRequest(BaseModel):
    file_id: str
    analysis_type: str

# Simple API endpoints for framework comparison

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """Upload a file and store it for analysis"""
    try:
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{file.filename}") as tmp_file:
            content = await file.read()
            tmp_file.write(content)
            temp_path = tmp_file.name

        # Store file path for later use
        file_id = f"file_{len(data_storage)}"
        data_storage[file_id] = {
            'filename': file.filename,
            'path': temp_path,
            'engines': {}  # Will store loaded data for each engine
        }

        return {
            'file_id': file_id,
            'filename': file.filename,
            'message': 'File uploaded successfully'
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/load")
async def load_data(request: AnalysisRequest):
    """Load data with specified engine and return basic info"""
    try:
        if request.file_id not in data_storage:
            raise HTTPException(status_code=404, detail="File not found")

        file_info = data_storage[request.file_id]
        engine = get_engine(request.engine_type)

        # Load data and get info
        result = engine.load_data(file_info['path'])

        # Store engine instance for later use
        file_info['engines'][request.engine_type] = engine

        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/analyze")
async def analyze_data(request: AnalysisRequest):
    """Perform analysis with specified engine"""
    try:
        if request.file_id not in data_storage:
            raise HTTPException(status_code=404, detail="File not found")

        file_info = data_storage[request.file_id]

        # Get or create engine instance
        if request.engine_type not in file_info['engines']:
            engine = get_engine(request.engine_type)
            engine.load_data(file_info['path'])
            file_info['engines'][request.engine_type] = engine
        else:
            engine = file_info['engines'][request.engine_type]

        # Perform analysis
        result = engine.analyze_data(request.analysis_type)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/preprocess")
async def preprocess_data(request: PreprocessRequest):
    """Apply preprocessing operations with specified engine"""
    try:
        if request.file_id not in data_storage:
            raise HTTPException(status_code=404, detail="File not found")

        file_info = data_storage[request.file_id]

        # Get or create engine instance
        if request.engine_type not in file_info['engines']:
            engine = get_engine(request.engine_type)
            engine.load_data(file_info['path'])
            file_info['engines'][request.engine_type] = engine
        else:
            engine = file_info['engines'][request.engine_type]

        # Apply preprocessing
        result = engine.preprocess_data(request.operations)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/compare")
async def compare_engines(request: ComparisonRequest):
    """Compare all three engines on the same analysis task"""
    try:
        if request.file_id not in data_storage:
            raise HTTPException(status_code=404, detail="File not found")

        file_info = data_storage[request.file_id]
        results = {}

        # Run analysis on all three engines
        for engine_type in ['pandas', 'polars', 'pyspark']:
            try:
                # Get or create engine instance
                if engine_type not in file_info['engines']:
                    engine = get_engine(engine_type)
                    engine.load_data(file_info['path'])
                    file_info['engines'][engine_type] = engine
                else:
                    engine = file_info['engines'][engine_type]

                # Perform analysis
                results[engine_type] = engine.analyze_data(request.analysis_type)
            except Exception as e:
                results[engine_type] = {'error': str(e), 'engine': engine_type}

        return {
            'comparison_results': results,
            'analysis_type': request.analysis_type
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/")
async def root():
    return {
        "message": "Simple Data Analysis Comparison Tool",
        "available_engines": ["pandas", "polars", "pyspark"],
        "analysis_types": ["basic_stats", "missing_values", "correlations", "group_analysis"]
    }

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)