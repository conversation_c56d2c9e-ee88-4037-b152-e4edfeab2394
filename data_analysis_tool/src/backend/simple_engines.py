# Simple unified interface for comparing pandas, polars, and pyspark
import pandas as pd
import polars as pl
from typing import Dict, Any, List, Union
import time

class SimpleDataEngine:
    """Base class with common interface for all engines"""
    
    def __init__(self, engine_name: str):
        self.engine_name = engine_name
        self.data = None
        
    def load_data(self, file_path: str) -> Dict[str, Any]:
        """Load data and return basic info"""
        start_time = time.time()
        
        try:
            self._load_file(file_path)
            load_time = time.time() - start_time
            
            return {
                'engine': self.engine_name,
                'load_time': round(load_time, 3),
                'shape': self._get_shape(),
                'columns': self._get_columns(),
                'memory_usage': self._get_memory_usage(),
                'preview': self._get_preview()
            }
        except Exception as e:
            return {'error': str(e), 'engine': self.engine_name}
    
    def analyze_data(self, analysis_type: str) -> Dict[str, Any]:
        """Perform analysis and return results with timing"""
        start_time = time.time()
        
        try:
            if analysis_type == 'basic_stats':
                result = self._basic_statistics()
            elif analysis_type == 'missing_values':
                result = self._missing_values()
            elif analysis_type == 'correlations':
                result = self._correlations()
            elif analysis_type == 'group_analysis':
                result = self._group_analysis()
            else:
                result = {'error': f'Unknown analysis type: {analysis_type}'}
            
            analysis_time = time.time() - start_time
            result['engine'] = self.engine_name
            result['analysis_time'] = round(analysis_time, 3)
            
            return result
        except Exception as e:
            return {'error': str(e), 'engine': self.engine_name}
    
    def preprocess_data(self, operations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Apply preprocessing operations with timing"""
        start_time = time.time()
        
        try:
            for op in operations:
                if op['type'] == 'drop_missing':
                    self._drop_missing()
                elif op['type'] == 'fill_missing':
                    self._fill_missing(op.get('method', 'mean'))
                elif op['type'] == 'drop_columns':
                    self._drop_columns(op['columns'])
            
            process_time = time.time() - start_time
            
            return {
                'engine': self.engine_name,
                'process_time': round(process_time, 3),
                'shape': self._get_shape(),
                'preview': self._get_preview()
            }
        except Exception as e:
            return {'error': str(e), 'engine': self.engine_name}
    
    # Abstract methods to be implemented by each engine
    def _load_file(self, file_path: str): pass
    def _get_shape(self): pass
    def _get_columns(self): pass
    def _get_memory_usage(self): pass
    def _get_preview(self): pass
    def _basic_statistics(self): pass
    def _missing_values(self): pass
    def _correlations(self): pass
    def _group_analysis(self): pass
    def _drop_missing(self): pass
    def _fill_missing(self, method: str): pass
    def _drop_columns(self, columns: List[str]): pass


class PandasEngine(SimpleDataEngine):
    """Pandas implementation"""
    
    def __init__(self):
        super().__init__("pandas")
    
    def _load_file(self, file_path: str):
        if file_path.endswith('.csv'):
            self.data = pd.read_csv(file_path)
        elif file_path.endswith(('.xlsx', '.xls')):
            self.data = pd.read_excel(file_path)
        elif file_path.endswith('.json'):
            self.data = pd.read_json(file_path)
    
    def _get_shape(self):
        return self.data.shape
    
    def _get_columns(self):
        return self.data.columns.tolist()
    
    def _get_memory_usage(self):
        return f"{self.data.memory_usage(deep=True).sum() / 1024**2:.2f} MB"
    
    def _get_preview(self):
        return self.data.head().to_dict('records')
    
    def _basic_statistics(self):
        return self.data.describe().to_dict()
    
    def _missing_values(self):
        return self.data.isnull().sum().to_dict()
    
    def _correlations(self):
        numeric_data = self.data.select_dtypes(include=['number'])
        return numeric_data.corr().to_dict()
    
    def _group_analysis(self):
        # Simple groupby on first categorical column
        cat_cols = self.data.select_dtypes(include=['object']).columns
        if len(cat_cols) > 0:
            return self.data.groupby(cat_cols[0]).size().to_dict()
        return {}
    
    def _drop_missing(self):
        self.data = self.data.dropna()
    
    def _fill_missing(self, method: str):
        if method == 'mean':
            self.data = self.data.fillna(self.data.mean())
        elif method == 'median':
            self.data = self.data.fillna(self.data.median())
        else:
            self.data = self.data.fillna(0)
    
    def _drop_columns(self, columns: List[str]):
        self.data = self.data.drop(columns=columns, errors='ignore')


class PolarsEngine(SimpleDataEngine):
    """Polars implementation"""
    
    def __init__(self):
        super().__init__("polars")
    
    def _load_file(self, file_path: str):
        if file_path.endswith('.csv'):
            self.data = pl.read_csv(file_path)
        elif file_path.endswith(('.xlsx', '.xls')):
            self.data = pl.read_excel(file_path)
        elif file_path.endswith('.json'):
            self.data = pl.read_json(file_path)
    
    def _get_shape(self):
        return (self.data.height, self.data.width)
    
    def _get_columns(self):
        return self.data.columns
    
    def _get_memory_usage(self):
        return f"{self.data.estimated_size('mb'):.2f} MB"
    
    def _get_preview(self):
        return self.data.head().to_pandas().to_dict('records')
    
    def _basic_statistics(self):
        return self.data.describe().to_pandas().to_dict()
    
    def _missing_values(self):
        return self.data.null_count().to_pandas().iloc[0].to_dict()
    
    def _correlations(self):
        numeric_data = self.data.select(pl.col(pl.NUMERIC_DTYPES))
        return numeric_data.corr().to_pandas().to_dict()
    
    def _group_analysis(self):
        # Simple groupby on first string column
        str_cols = [col for col in self.data.columns if self.data[col].dtype == pl.Utf8]
        if str_cols:
            return self.data.group_by(str_cols[0]).len().to_pandas().set_index(str_cols[0])['len'].to_dict()
        return {}
    
    def _drop_missing(self):
        self.data = self.data.drop_nulls()
    
    def _fill_missing(self, method: str):
        if method == 'mean':
            self.data = self.data.fill_null(strategy='mean')
        elif method == 'median':
            self.data = self.data.fill_null(strategy='median')
        else:
            self.data = self.data.fill_null(0)
    
    def _drop_columns(self, columns: List[str]):
        existing_cols = [col for col in columns if col in self.data.columns]
        if existing_cols:
            self.data = self.data.drop(existing_cols)


class PySparkEngine(SimpleDataEngine):
    """PySpark implementation"""
    
    def __init__(self):
        super().__init__("pyspark")
        from pyspark.sql import SparkSession
        self.spark = SparkSession.builder.appName("DataAnalysis").getOrCreate()
    
    def _load_file(self, file_path: str):
        if file_path.endswith('.csv'):
            self.data = self.spark.read.csv(file_path, header=True, inferSchema=True)
        elif file_path.endswith('.json'):
            self.data = self.spark.read.json(file_path)
        # Note: PySpark doesn't natively support Excel
    
    def _get_shape(self):
        return (self.data.count(), len(self.data.columns))
    
    def _get_columns(self):
        return self.data.columns
    
    def _get_memory_usage(self):
        return "N/A (Distributed)"
    
    def _get_preview(self):
        return [row.asDict() for row in self.data.head(5)]
    
    def _basic_statistics(self):
        return self.data.describe().toPandas().to_dict()
    
    def _missing_values(self):
        from pyspark.sql.functions import col, isnan, when, count
        return {c: self.data.select(count(when(col(c).isNull(), c))).collect()[0][0] 
                for c in self.data.columns}
    
    def _correlations(self):
        numeric_cols = [f.name for f in self.data.schema.fields if str(f.dataType) in ['IntegerType', 'DoubleType', 'FloatType']]
        if numeric_cols:
            return self.data.select(numeric_cols).toPandas().corr().to_dict()
        return {}
    
    def _group_analysis(self):
        string_cols = [f.name for f in self.data.schema.fields if str(f.dataType) == 'StringType']
        if string_cols:
            return {row[string_cols[0]]: row['count'] 
                   for row in self.data.groupBy(string_cols[0]).count().collect()}
        return {}
    
    def _drop_missing(self):
        self.data = self.data.dropna()
    
    def _fill_missing(self, method: str):
        if method in ['mean', 'median']:
            # Simple fill with 0 for demo
            self.data = self.data.fillna(0)
        else:
            self.data = self.data.fillna(0)
    
    def _drop_columns(self, columns: List[str]):
        existing_cols = [col for col in columns if col in self.data.columns]
        if existing_cols:
            self.data = self.data.drop(*existing_cols)


# Simple factory function
def get_engine(engine_type: str) -> SimpleDataEngine:
    """Get the appropriate engine instance"""
    engines = {
        'pandas': PandasEngine,
        'polars': PolarsEngine,
        'pyspark': PySparkEngine
    }
    
    if engine_type not in engines:
        raise ValueError(f"Unknown engine: {engine_type}")
    
    return engines[engine_type]()
